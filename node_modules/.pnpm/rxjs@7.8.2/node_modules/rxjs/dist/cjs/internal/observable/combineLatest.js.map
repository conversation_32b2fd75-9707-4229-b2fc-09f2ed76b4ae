{"version": 3, "file": "combineLatest.js", "sourceRoot": "", "sources": ["../../../../src/internal/observable/combineLatest.ts"], "names": [], "mappings": ";;;AAAA,4CAA2C;AAE3C,qEAAoE;AAEpE,+BAA8B;AAC9B,6CAA4C;AAE5C,6DAA4D;AAC5D,qCAA+D;AAC/D,qDAAoD;AACpD,sEAA2E;AAE3E,2DAA0D;AAwL1D,SAAgB,aAAa;IAAoC,cAAc;SAAd,UAAc,EAAd,qBAAc,EAAd,IAAc;QAAd,yBAAc;;IAC7E,IAAM,SAAS,GAAG,mBAAY,CAAC,IAAI,CAAC,CAAC;IACrC,IAAM,cAAc,GAAG,wBAAiB,CAAC,IAAI,CAAC,CAAC;IAEzC,IAAA,KAA8B,2CAAoB,CAAC,IAAI,CAAC,EAAhD,WAAW,UAAA,EAAE,IAAI,UAA+B,CAAC;IAE/D,IAAI,WAAW,CAAC,MAAM,KAAK,CAAC,EAAE;QAI5B,OAAO,WAAI,CAAC,EAAE,EAAE,SAAgB,CAAC,CAAC;KACnC;IAED,IAAM,MAAM,GAAG,IAAI,uBAAU,CAC3B,iBAAiB,CACf,WAAoD,EACpD,SAAS,EACT,IAAI;QACF,CAAC;YACC,UAAC,MAAM,IAAK,OAAA,2BAAY,CAAC,IAAI,EAAE,MAAM,CAAC,EAA1B,CAA0B;QACxC,CAAC;YACC,mBAAQ,CACb,CACF,CAAC;IAEF,OAAO,cAAc,CAAC,CAAC,CAAE,MAAM,CAAC,IAAI,CAAC,mCAAgB,CAAC,cAAc,CAAC,CAAmB,CAAC,CAAC,CAAC,MAAM,CAAC;AACpG,CAAC;AA1BD,sCA0BC;AAED,SAAgB,iBAAiB,CAC/B,WAAmC,EACnC,SAAyB,EACzB,cAAiD;IAAjD,+BAAA,EAAA,iBAAyC,mBAAQ;IAEjD,OAAO,UAAC,UAA2B;QAGjC,aAAa,CACX,SAAS,EACT;YACU,IAAA,MAAM,GAAK,WAAW,OAAhB,CAAiB;YAE/B,IAAM,MAAM,GAAG,IAAI,KAAK,CAAC,MAAM,CAAC,CAAC;YAGjC,IAAI,MAAM,GAAG,MAAM,CAAC;YAIpB,IAAI,oBAAoB,GAAG,MAAM,CAAC;oCAGzB,CAAC;gBACR,aAAa,CACX,SAAS,EACT;oBACE,IAAM,MAAM,GAAG,WAAI,CAAC,WAAW,CAAC,CAAC,CAAC,EAAE,SAAgB,CAAC,CAAC;oBACtD,IAAI,aAAa,GAAG,KAAK,CAAC;oBAC1B,MAAM,CAAC,SAAS,CACd,6CAAwB,CACtB,UAAU,EACV,UAAC,KAAK;wBAEJ,MAAM,CAAC,CAAC,CAAC,GAAG,KAAK,CAAC;wBAClB,IAAI,CAAC,aAAa,EAAE;4BAElB,aAAa,GAAG,IAAI,CAAC;4BACrB,oBAAoB,EAAE,CAAC;yBACxB;wBACD,IAAI,CAAC,oBAAoB,EAAE;4BAGzB,UAAU,CAAC,IAAI,CAAC,cAAc,CAAC,MAAM,CAAC,KAAK,EAAE,CAAC,CAAC,CAAC;yBACjD;oBACH,CAAC,EACD;wBACE,IAAI,CAAC,EAAE,MAAM,EAAE;4BAGb,UAAU,CAAC,QAAQ,EAAE,CAAC;yBACvB;oBACH,CAAC,CACF,CACF,CAAC;gBACJ,CAAC,EACD,UAAU,CACX,CAAC;;YAlCJ,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,MAAM,EAAE,CAAC,EAAE;wBAAtB,CAAC;aAmCT;QACH,CAAC,EACD,UAAU,CACX,CAAC;IACJ,CAAC,CAAC;AACJ,CAAC;AA/DD,8CA+DC;AAMD,SAAS,aAAa,CAAC,SAAoC,EAAE,OAAmB,EAAE,YAA0B;IAC1G,IAAI,SAAS,EAAE;QACb,iCAAe,CAAC,YAAY,EAAE,SAAS,EAAE,OAAO,CAAC,CAAC;KACnD;SAAM;QACL,OAAO,EAAE,CAAC;KACX;AACH,CAAC"}