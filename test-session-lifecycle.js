#!/usr/bin/env node

// Simple test script to verify session lifecycle improvements
const fetch = require('node-fetch');

const BASE_URL = 'http://localhost:3001';

async function testSessionLifecycle() {
  console.log('🧪 Testing Session Lifecycle Improvements...\n');

  try {
    // Test 1: Check tmux sessions endpoint
    console.log('1. Testing tmux sessions endpoint...');
    const tmuxResponse = await fetch(`${BASE_URL}/api/sessions/tmux`);
    if (tmuxResponse.ok) {
      const sessions = await tmuxResponse.json();
      console.log(`✅ Found ${sessions.length} tmux sessions:`, sessions.map(s => s.name));
    } else {
      console.log('❌ Failed to get tmux sessions');
    }

    // Test 2: Check session status endpoint
    console.log('\n2. Testing session status endpoint...');
    const statusResponse = await fetch(`${BASE_URL}/api/session/test-project/test-session/status`);
    if (statusResponse.ok) {
      const status = await statusResponse.json();
      console.log('✅ Session status check works:', status);
    } else {
      console.log('❌ Failed to check session status');
    }

    // Test 3: Start a test session
    console.log('\n3. Testing session start...');
    const startResponse = await fetch(`${BASE_URL}/api/terminal/start`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        projectId: 'test-project',
        sessionId: 'test-session-id',
        projectPath: process.cwd(),
        sessionName: 'test-session',
        prompt: 'Test session for lifecycle'
      })
    });

    if (startResponse.ok) {
      const result = await startResponse.json();
      console.log('✅ Session started successfully:', result.terminalId);
      
      // Wait a bit then check if tmux session was created
      setTimeout(async () => {
        console.log('\n4. Checking if tmux session was created...');
        const newTmuxResponse = await fetch(`${BASE_URL}/api/sessions/tmux`);
        if (newTmuxResponse.ok) {
          const newSessions = await newTmuxResponse.json();
          const testSession = newSessions.find(s => s.name.includes('test-project'));
          if (testSession) {
            console.log('✅ Tmux session created:', testSession.name);
          } else {
            console.log('❌ Test tmux session not found');
          }
        }
      }, 2000);

    } else {
      const error = await startResponse.json();
      console.log('❌ Failed to start session:', error);
    }

  } catch (error) {
    console.error('❌ Test failed:', error.message);
  }
}

// Run the test
testSessionLifecycle();
