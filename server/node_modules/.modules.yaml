hoistPattern:
  - '*'
hoistedDependencies:
  '@socket.io/component-emitter@3.1.2':
    '@socket.io/component-emitter': private
  '@types/cors@2.8.19':
    '@types/cors': private
  '@types/node@24.0.10':
    '@types/node': private
  accepts@1.3.8:
    accepts: private
  anymatch@3.1.3:
    anymatch: private
  array-flatten@1.1.1:
    array-flatten: private
  balanced-match@1.0.2:
    balanced-match: private
  base64id@2.0.0:
    base64id: private
  binary-extensions@2.3.0:
    binary-extensions: private
  body-parser@1.20.3:
    body-parser: private
  brace-expansion@1.1.12:
    brace-expansion: private
  braces@3.0.3:
    braces: private
  bytes@3.1.2:
    bytes: private
  call-bind-apply-helpers@1.0.2:
    call-bind-apply-helpers: private
  call-bound@1.0.4:
    call-bound: private
  concat-map@0.0.1:
    concat-map: private
  content-disposition@0.5.4:
    content-disposition: private
  content-type@1.0.5:
    content-type: private
  cookie-signature@1.0.6:
    cookie-signature: private
  cookie@0.7.1:
    cookie: private
  debug@2.6.9:
    debug: private
  depd@2.0.0:
    depd: private
  destroy@1.2.0:
    destroy: private
  dunder-proto@1.0.1:
    dunder-proto: private
  ee-first@1.1.1:
    ee-first: private
  encodeurl@2.0.0:
    encodeurl: private
  engine.io-parser@5.2.3:
    engine.io-parser: private
  engine.io@6.6.4:
    engine.io: private
  es-define-property@1.0.1:
    es-define-property: private
  es-errors@1.3.0:
    es-errors: private
  es-object-atoms@1.1.1:
    es-object-atoms: private
  escape-html@1.0.3:
    escape-html: private
  etag@1.8.1:
    etag: private
  fill-range@7.1.1:
    fill-range: private
  finalhandler@1.3.1:
    finalhandler: private
  forwarded@0.2.0:
    forwarded: private
  fresh@0.5.2:
    fresh: private
  fsevents@2.3.3:
    fsevents: private
  function-bind@1.1.2:
    function-bind: private
  get-intrinsic@1.3.0:
    get-intrinsic: private
  get-proto@1.0.1:
    get-proto: private
  glob-parent@5.1.2:
    glob-parent: private
  gopd@1.2.0:
    gopd: private
  graceful-fs@4.2.11:
    graceful-fs: private
  has-flag@3.0.0:
    has-flag: private
  has-symbols@1.1.0:
    has-symbols: private
  hasown@2.0.2:
    hasown: private
  http-errors@2.0.0:
    http-errors: private
  iconv-lite@0.4.24:
    iconv-lite: private
  ignore-by-default@1.0.1:
    ignore-by-default: private
  inherits@2.0.4:
    inherits: private
  ipaddr.js@1.9.1:
    ipaddr.js: private
  is-binary-path@2.1.0:
    is-binary-path: private
  is-extglob@2.1.1:
    is-extglob: private
  is-glob@4.0.3:
    is-glob: private
  is-number@7.0.0:
    is-number: private
  jsonfile@6.1.0:
    jsonfile: private
  math-intrinsics@1.1.0:
    math-intrinsics: private
  media-typer@0.3.0:
    media-typer: private
  merge-descriptors@1.0.3:
    merge-descriptors: private
  methods@1.1.2:
    methods: private
  mime-db@1.52.0:
    mime-db: private
  mime-types@2.1.35:
    mime-types: private
  mime@1.6.0:
    mime: private
  minimatch@3.1.2:
    minimatch: private
  ms@2.0.0:
    ms: private
  nan@2.22.2:
    nan: private
  negotiator@0.6.3:
    negotiator: private
  normalize-path@3.0.0:
    normalize-path: private
  object-assign@4.1.1:
    object-assign: private
  object-inspect@1.13.4:
    object-inspect: private
  on-finished@2.4.1:
    on-finished: private
  parseurl@1.3.3:
    parseurl: private
  path-to-regexp@0.1.12:
    path-to-regexp: private
  picomatch@2.3.1:
    picomatch: private
  proxy-addr@2.0.7:
    proxy-addr: private
  pstree.remy@1.1.8:
    pstree.remy: private
  qs@6.13.0:
    qs: private
  range-parser@1.2.1:
    range-parser: private
  raw-body@2.5.2:
    raw-body: private
  readdirp@3.6.0:
    readdirp: private
  safe-buffer@5.1.2:
    safe-buffer: private
  safe-buffer@5.2.1:
    safe-buffer: private
  safer-buffer@2.1.2:
    safer-buffer: private
  semver@7.7.2:
    semver: private
  send@0.19.0:
    send: private
  serve-static@1.16.2:
    serve-static: private
  setprototypeof@1.2.0:
    setprototypeof: private
  side-channel-list@1.0.0:
    side-channel-list: private
  side-channel-map@1.0.1:
    side-channel-map: private
  side-channel-weakmap@1.0.2:
    side-channel-weakmap: private
  side-channel@1.1.0:
    side-channel: private
  simple-update-notifier@2.0.0:
    simple-update-notifier: private
  socket.io-adapter@2.5.5:
    socket.io-adapter: private
  socket.io-parser@4.2.4:
    socket.io-parser: private
  statuses@2.0.1:
    statuses: private
  supports-color@5.5.0:
    supports-color: private
  to-regex-range@5.0.1:
    to-regex-range: private
  toidentifier@1.0.1:
    toidentifier: private
  touch@3.1.1:
    touch: private
  type-is@1.6.18:
    type-is: private
  undefsafe@2.0.5:
    undefsafe: private
  undici-types@7.8.0:
    undici-types: private
  universalify@2.0.1:
    universalify: private
  unpipe@1.0.0:
    unpipe: private
  utils-merge@1.0.1:
    utils-merge: private
  vary@1.1.2:
    vary: private
  ws@8.17.1:
    ws: private
included:
  dependencies: true
  devDependencies: true
  optionalDependencies: true
injectedDeps: {}
layoutVersion: 5
nodeLinker: isolated
packageManager: pnpm@10.12.4
pendingBuilds: []
prunedAt: Fri, 04 Jul 2025 01:28:20 GMT
publicHoistPattern: []
registries:
  '@jsr': https://npm.jsr.io/
  default: https://registry.npmjs.org/
skipped: []
storeDir: /Users/<USER>/Library/pnpm/store/v10
virtualStoreDir: .pnpm
virtualStoreDirMaxLength: 120
