const express = require('express');
const http = require('http');
const socketIo = require('socket.io');
const cors = require('cors');
const path = require('path');
const fs = require('fs-extra');
const os = require('os');

const TerminalManager = require('./services/TerminalManager');
const ProjectManager = require('./services/ProjectManager');
const SettingsManager = require('./services/SettingsManager');

const app = express();
const server = http.createServer(app);
const io = socketIo(server, {
  cors: {
    origin: "http://localhost:3000",
    methods: ["GET", "POST"]
  }
});

// Middleware
app.use(cors());
app.use(express.json());

// Initialize managers
const terminalManager = new TerminalManager(io);
const projectManager = new ProjectManager();
const settingsManager = new SettingsManager();

// API Routes
app.get('/api/health', (req, res) => {
  res.json({ status: 'ok', timestamp: new Date().toISOString() });
});

// Project routes
app.get('/api/projects', async (req, res) => {
  try {
    const projects = await projectManager.getProjects();
    res.json(projects);
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
});

app.post('/api/projects', async (req, res) => {
  try {
    const { name, path: projectPath } = req.body;
    const project = await projectManager.createProject(name, projectPath);
    res.json(project);
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
});

app.delete('/api/projects/:id', async (req, res) => {
  try {
    await projectManager.deleteProject(req.params.id);
    res.json({ success: true });
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
});

// Session routes
app.get('/api/projects/:projectId/sessions', async (req, res) => {
  try {
    const sessions = await projectManager.getSessions(req.params.projectId);
    res.json(sessions);
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
});

app.post('/api/projects/:projectId/sessions', async (req, res) => {
  try {
    const { name, prompt } = req.body;
    const session = await projectManager.createSession(req.params.projectId, name, prompt);
    res.json(session);
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
});

app.delete('/api/projects/:projectId/sessions/:sessionId', async (req, res) => {
  try {
    const { projectId, sessionId } = req.params;

    // First, kill any running terminal session
    const terminalData = terminalManager.getTerminalBySessionId(sessionId);
    if (terminalData) {
      console.log(`[API] Killing terminal session for session ${sessionId}`);
      await terminalManager.killSession(terminalData.id);
    }

    // Then delete the session from the project
    await projectManager.deleteSession(projectId, sessionId);
    res.json({ success: true });
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
});

// Settings routes
app.get('/api/settings', async (req, res) => {
  try {
    const settings = await settingsManager.getSettings();
    res.json(settings);
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
});

app.put('/api/settings', async (req, res) => {
  try {
    await settingsManager.updateSettings(req.body);
    res.json({ success: true });
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
});

app.get('/api/settings/info', async (req, res) => {
  try {
    const info = await settingsManager.getSettingsInfo();
    res.json(info);
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
});

app.post('/api/settings/validate', async (req, res) => {
  try {
    const { jsonString } = req.body;
    const result = await settingsManager.validateSettingsJson(jsonString);
    res.json(result);
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
});

app.get('/api/gemini-md', async (req, res) => {
  try {
    const content = await settingsManager.getGeminiMd();
    res.json({ content });
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
});

app.put('/api/gemini-md', async (req, res) => {
  try {
    const { content } = req.body;
    await settingsManager.updateGeminiMd(content);
    res.json({ success: true });
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
});

// Terminal routes
app.post('/api/terminal/start', async (req, res) => {
  try {
    const { projectId, sessionId, projectPath, sessionName, prompt } = req.body;
    const terminalId = await terminalManager.startSession(projectId, sessionId, projectPath, sessionName, prompt);
    res.json({ terminalId });
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
});

app.post('/api/terminal/:terminalId/input', async (req, res) => {
  try {
    const { input } = req.body;
    await terminalManager.sendInput(req.params.terminalId, input);
    res.json({ success: true });
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
});

app.delete('/api/terminal/:terminalId', async (req, res) => {
  try {
    await terminalManager.killSession(req.params.terminalId);
    res.json({ success: true });
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
});

app.get('/api/terminal/list', async (req, res) => {
  try {
    const terminals = terminalManager.getAllTerminals();
    res.json(terminals);
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
});

app.post('/api/terminal/:terminalId/resize', async (req, res) => {
  try {
    const { cols, rows } = req.body;
    terminalManager.resizeTerminal(req.params.terminalId, cols, rows);
    res.json({ success: true });
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
});

// Session status routes
app.get('/api/session/:projectId/:sessionName/status', async (req, res) => {
  try {
    const { projectId, sessionName } = req.params;
    const tmuxSessionName = `gemini-${projectId}-${sessionName.replace(/\s+/g, '-').toLowerCase()}`;
    const status = await terminalManager.getTmuxSessionStatus(tmuxSessionName);
    res.json(status);
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
});

app.get('/api/sessions/tmux', async (req, res) => {
  try {
    const sessions = await terminalManager.getAllTmuxSessions();
    res.json(sessions);
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
});

// System info routes
app.get('/api/system/gemini-cli', async (req, res) => {
  try {
    const available = await projectManager.checkGeminiCLI();
    const version = available ? await projectManager.getGeminiVersion() : null;
    res.json({ available, version });
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
});

// Socket.IO connection handling
io.on('connection', (socket) => {
  console.log('Client connected:', socket.id);
  
  socket.on('disconnect', () => {
    console.log('Client disconnected:', socket.id);
  });
});

const PORT = process.env.PORT || 3001;
server.listen(PORT, () => {
  console.log(`Server running on port ${PORT}`);
  console.log(`Gemini CLI directory: ${path.join(os.homedir(), '.gemini')}`);
});
