const { spawn, exec } = require('child_process');
const fs = require('fs-extra');
const path = require('path');
const os = require('os');
const crypto = require('crypto');

class CloudflareTunnelManager {
  constructor() {
    this.cloudflaredPath = this.findCloudflaredPath();
    this.configDir = path.join(os.homedir(), '.cloudflared');
    this.tunnelConfigPath = path.join(this.configDir, 'config.yml');
    this.runningProcesses = new Map(); // tunnelId -> process
  }

  findCloudflaredPath() {
    // Try common installation paths
    const possiblePaths = [
      'cloudflared', // In PATH
      '/usr/local/bin/cloudflared',
      '/usr/bin/cloudflared',
      '/opt/homebrew/bin/cloudflared',
      path.join(os.homedir(), '.local/bin/cloudflared')
    ];

    for (const cloudflaredPath of possiblePaths) {
      try {
        // Test if cloudflared exists and is executable
        require('child_process').execSync(`${cloudflaredPath} --version`, { stdio: 'ignore' });
        return cloudflaredPath;
      } catch (error) {
        continue;
      }
    }
    
    throw new Error('cloudflared not found. Please install cloudflared first.');
  }

  async ensureCloudflaredDir() {
    await fs.ensureDir(this.configDir);
  }

  async authenticateCloudflared(apiToken) {
    return new Promise((resolve, reject) => {
      const env = { ...process.env, CLOUDFLARE_API_TOKEN: apiToken };
      
      // Use API token authentication instead of interactive login
      const authProcess = spawn(this.cloudflaredPath, ['tunnel', 'login'], {
        env,
        stdio: ['pipe', 'pipe', 'pipe']
      });

      let output = '';
      let errorOutput = '';

      authProcess.stdout.on('data', (data) => {
        output += data.toString();
      });

      authProcess.stderr.on('data', (data) => {
        errorOutput += data.toString();
      });

      authProcess.on('close', (code) => {
        if (code === 0) {
          resolve({ success: true, output });
        } else {
          reject(new Error(`Authentication failed: ${errorOutput || output}`));
        }
      });

      authProcess.on('error', (error) => {
        reject(new Error(`Failed to start cloudflared: ${error.message}`));
      });
    });
  }

  async createTunnel(tunnelName, apiToken) {
    await this.ensureCloudflaredDir();
    
    return new Promise((resolve, reject) => {
      const env = { ...process.env, CLOUDFLARE_API_TOKEN: apiToken };
      
      const createProcess = spawn(this.cloudflaredPath, ['tunnel', 'create', tunnelName], {
        env,
        stdio: ['pipe', 'pipe', 'pipe']
      });

      let output = '';
      let errorOutput = '';

      createProcess.stdout.on('data', (data) => {
        output += data.toString();
      });

      createProcess.stderr.on('data', (data) => {
        errorOutput += data.toString();
      });

      createProcess.on('close', (code) => {
        if (code === 0) {
          // Extract tunnel ID from output
          const tunnelIdMatch = output.match(/Created tunnel (\w+) with name (\w+)/);
          if (tunnelIdMatch) {
            const tunnelId = tunnelIdMatch[1];
            resolve({ 
              success: true, 
              tunnelId, 
              tunnelName,
              output 
            });
          } else {
            reject(new Error('Could not extract tunnel ID from output'));
          }
        } else {
          reject(new Error(`Tunnel creation failed: ${errorOutput || output}`));
        }
      });

      createProcess.on('error', (error) => {
        reject(new Error(`Failed to start cloudflared: ${error.message}`));
      });
    });
  }

  async createTunnelConfig(tunnelId, tunnelName, localPort = 3000, auth = null) {
    const configContent = {
      tunnel: tunnelId,
      'credentials-file': path.join(this.configDir, `${tunnelId}.json`),
      ingress: []
    };

    // Add basic auth if provided
    if (auth && auth.username && auth.password) {
      configContent.ingress.push({
        hostname: `${tunnelName}.example.com`, // This will be updated with actual hostname
        service: `http://localhost:${localPort}`,
        originRequest: {
          httpHostHeader: `localhost:${localPort}`
        }
      });
    } else {
      configContent.ingress.push({
        service: `http://localhost:${localPort}`
      });
    }

    // Default catch-all rule
    configContent.ingress.push({
      service: 'http_status:404'
    });

    const yamlContent = this.objectToYaml(configContent);
    await fs.writeFile(this.tunnelConfigPath, yamlContent, 'utf8');
    
    return this.tunnelConfigPath;
  }

  objectToYaml(obj, indent = 0) {
    let yaml = '';
    const spaces = '  '.repeat(indent);
    
    for (const [key, value] of Object.entries(obj)) {
      if (Array.isArray(value)) {
        yaml += `${spaces}${key}:\n`;
        for (const item of value) {
          if (typeof item === 'object') {
            yaml += `${spaces}- `;
            const itemYaml = this.objectToYaml(item, indent + 1);
            yaml += itemYaml.replace(/^  /, '').replace(/\n  /g, '\n' + spaces + '  ');
          } else {
            yaml += `${spaces}- ${item}\n`;
          }
        }
      } else if (typeof value === 'object' && value !== null) {
        yaml += `${spaces}${key}:\n`;
        yaml += this.objectToYaml(value, indent + 1);
      } else {
        yaml += `${spaces}${key}: ${value}\n`;
      }
    }
    
    return yaml;
  }

  async routeTunnel(tunnelId, hostname, apiToken) {
    return new Promise((resolve, reject) => {
      const env = { ...process.env, CLOUDFLARE_API_TOKEN: apiToken };
      
      const routeProcess = spawn(this.cloudflaredPath, [
        'tunnel', 'route', 'dns', tunnelId, hostname
      ], {
        env,
        stdio: ['pipe', 'pipe', 'pipe']
      });

      let output = '';
      let errorOutput = '';

      routeProcess.stdout.on('data', (data) => {
        output += data.toString();
      });

      routeProcess.stderr.on('data', (data) => {
        errorOutput += data.toString();
      });

      routeProcess.on('close', (code) => {
        if (code === 0) {
          resolve({ success: true, output, hostname });
        } else {
          reject(new Error(`Route creation failed: ${errorOutput || output}`));
        }
      });

      routeProcess.on('error', (error) => {
        reject(new Error(`Failed to start cloudflared: ${error.message}`));
      });
    });
  }

  async startQuickTunnel(localPort = 3000) {
    const tunnelId = 'quick-tunnel';

    if (this.runningProcesses.has(tunnelId)) {
      throw new Error('Quick tunnel is already running');
    }

    return new Promise((resolve, reject) => {
      const runProcess = spawn(this.cloudflaredPath, [
        'tunnel', '--url', `http://localhost:${localPort}`
      ], {
        stdio: ['pipe', 'pipe', 'pipe'],
        detached: false
      });

      this.runningProcesses.set(tunnelId, runProcess);

      let output = '';
      let errorOutput = '';
      let resolved = false;
      let tunnelUrl = null;

      runProcess.stdout.on('data', (data) => {
        const dataStr = data.toString();
        output += dataStr;
        console.log('Cloudflared output:', dataStr);

        // Look for trycloudflare.com URL
        if (dataStr.includes('trycloudflare.com') && dataStr.includes('https://')) {
          const urlMatch = dataStr.match(/https:\/\/[a-zA-Z0-9-]+\.trycloudflare\.com/);
          if (urlMatch && !resolved) {
            tunnelUrl = urlMatch[0];
            resolved = true;
            resolve({
              success: true,
              process: runProcess,
              output,
              tunnelUrl,
              tunnelId
            });
          }
        }
      });

      runProcess.stderr.on('data', (data) => {
        const dataStr = data.toString();
        errorOutput += dataStr;
        console.log('Cloudflared stderr:', dataStr);

        // Sometimes the URL appears in stderr
        if (dataStr.includes('trycloudflare.com') && dataStr.includes('https://')) {
          const urlMatch = dataStr.match(/https:\/\/[a-zA-Z0-9-]+\.trycloudflare\.com/);
          if (urlMatch && !resolved) {
            tunnelUrl = urlMatch[0];
            resolved = true;
            resolve({
              success: true,
              process: runProcess,
              output: errorOutput,
              tunnelUrl,
              tunnelId
            });
          }
        }
      });

      runProcess.on('close', (code) => {
        this.runningProcesses.delete(tunnelId);
        if (!resolved) {
          reject(new Error(`Quick tunnel failed to start: ${errorOutput || output}`));
        }
      });

      runProcess.on('error', (error) => {
        this.runningProcesses.delete(tunnelId);
        if (!resolved) {
          reject(new Error(`Failed to start quick tunnel: ${error.message}`));
        }
      });

      // Timeout after 30 seconds if no URL is found
      setTimeout(() => {
        if (!resolved) {
          this.stopTunnel(tunnelId);
          reject(new Error('Quick tunnel startup timeout - no URL found'));
        }
      }, 30000);
    });
  }

  async startTunnel(tunnelId, apiToken) {
    if (this.runningProcesses.has(tunnelId)) {
      throw new Error('Tunnel is already running');
    }

    return new Promise((resolve, reject) => {
      const env = { ...process.env, CLOUDFLARE_API_TOKEN: apiToken };

      const runProcess = spawn(this.cloudflaredPath, [
        'tunnel', '--config', this.tunnelConfigPath, 'run', tunnelId
      ], {
        env,
        stdio: ['pipe', 'pipe', 'pipe'],
        detached: false
      });

      this.runningProcesses.set(tunnelId, runProcess);

      let output = '';
      let errorOutput = '';
      let resolved = false;

      runProcess.stdout.on('data', (data) => {
        const dataStr = data.toString();
        output += dataStr;

        // Look for successful connection indicators
        if (dataStr.includes('Connection') && dataStr.includes('registered') && !resolved) {
          resolved = true;
          resolve({ success: true, process: runProcess, output });
        }
      });

      runProcess.stderr.on('data', (data) => {
        errorOutput += data.toString();
      });

      runProcess.on('close', (code) => {
        this.runningProcesses.delete(tunnelId);
        if (!resolved) {
          reject(new Error(`Tunnel failed to start: ${errorOutput || output}`));
        }
      });

      runProcess.on('error', (error) => {
        this.runningProcesses.delete(tunnelId);
        if (!resolved) {
          reject(new Error(`Failed to start tunnel: ${error.message}`));
        }
      });

      // Timeout after 30 seconds if no connection is established
      setTimeout(() => {
        if (!resolved) {
          this.stopTunnel(tunnelId);
          reject(new Error('Tunnel startup timeout'));
        }
      }, 30000);
    });
  }

  async stopTunnel(tunnelId) {
    const process = this.runningProcesses.get(tunnelId);
    if (process) {
      process.kill('SIGTERM');
      this.runningProcesses.delete(tunnelId);
      return { success: true };
    }
    return { success: false, error: 'Tunnel not running' };
  }

  async getTunnelStatus(tunnelId) {
    const isRunning = this.runningProcesses.has(tunnelId);
    return {
      tunnelId,
      status: isRunning ? 'running' : 'stopped',
      isRunning
    };
  }

  async listTunnels(apiToken) {
    return new Promise((resolve, reject) => {
      const env = { ...process.env, CLOUDFLARE_API_TOKEN: apiToken };
      
      const listProcess = spawn(this.cloudflaredPath, ['tunnel', 'list'], {
        env,
        stdio: ['pipe', 'pipe', 'pipe']
      });

      let output = '';
      let errorOutput = '';

      listProcess.stdout.on('data', (data) => {
        output += data.toString();
      });

      listProcess.stderr.on('data', (data) => {
        errorOutput += data.toString();
      });

      listProcess.on('close', (code) => {
        if (code === 0) {
          resolve({ success: true, output });
        } else {
          reject(new Error(`Failed to list tunnels: ${errorOutput || output}`));
        }
      });

      listProcess.on('error', (error) => {
        reject(new Error(`Failed to start cloudflared: ${error.message}`));
      });
    });
  }

  generateTunnelName() {
    const timestamp = Date.now().toString(36);
    const random = crypto.randomBytes(4).toString('hex');
    return `gemi-tunnel-${timestamp}-${random}`;
  }
}

module.exports = CloudflareTunnelManager;
