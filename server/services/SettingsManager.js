const fs = require('fs-extra');
const path = require('path');
const os = require('os');

class SettingsManager {
  constructor() {
    this.settingsFile = path.join(os.homedir(), '.gemini', 'settings.json');
    this.geminiMdFile = path.join(os.homedir(), '.gemini', 'GEMINI.md');
    this.ensureSettingsFile();
  }

  async ensureSettingsFile() {
    const geminiDir = path.join(os.homedir(), '.gemini');
    await fs.ensureDir(geminiDir);
    
    if (!await fs.pathExists(this.settingsFile)) {
      const defaultSettings = {
        theme: "Default",
        autoAccept: false,
        sandbox: false,
        fileFiltering: {
          respectGitIgnore: true,
          enableRecursiveFileSearch: true
        },
        telemetry: {
          enabled: false,
          target: "local",
          otlpEndpoint: "http://localhost:4317",
          logPrompts: true
        },
        usageStatisticsEnabled: true,
        hideTips: false,
        cloudflare: {
          tunnel: {
            enabled: false,
            apiToken: "",
            tunnelName: "",
            tunnelId: "",
            tunnelUrl: "",
            auth: {
              username: "",
              password: ""
            },
            status: "stopped" // stopped, starting, running, error
          }
        }
      };
      
      await fs.writeJson(this.settingsFile, defaultSettings, { spaces: 2 });
    }
  }

  async getSettings() {
    try {
      const settings = await fs.readJson(this.settingsFile);
      return settings;
    } catch (error) {
      console.error('Error reading settings file:', error);
      // Return default settings if file is corrupted
      return {
        theme: "Default",
        autoAccept: false,
        sandbox: false,
        fileFiltering: {
          respectGitIgnore: true,
          enableRecursiveFileSearch: true
        },
        telemetry: {
          enabled: false,
          target: "local",
          otlpEndpoint: "http://localhost:4317",
          logPrompts: true
        },
        usageStatisticsEnabled: true,
        hideTips: false,
        cloudflare: {
          tunnel: {
            enabled: false,
            apiToken: "",
            tunnelName: "",
            tunnelId: "",
            tunnelUrl: "",
            auth: {
              username: "",
              password: ""
            },
            status: "stopped"
          }
        }
      };
    }
  }

  async updateSettings(newSettings) {
    try {
      // Validate JSON structure
      if (typeof newSettings !== 'object' || newSettings === null) {
        throw new Error('Settings must be a valid JSON object');
      }

      // Read current settings to preserve any fields not being updated
      const currentSettings = await this.getSettings();
      const mergedSettings = { ...currentSettings, ...newSettings };

      // Validate specific fields if they exist
      if (mergedSettings.theme && typeof mergedSettings.theme !== 'string') {
        throw new Error('Theme must be a string');
      }

      if (mergedSettings.autoAccept !== undefined && typeof mergedSettings.autoAccept !== 'boolean') {
        throw new Error('autoAccept must be a boolean');
      }

      if (mergedSettings.sandbox !== undefined && 
          typeof mergedSettings.sandbox !== 'boolean' && 
          typeof mergedSettings.sandbox !== 'string') {
        throw new Error('sandbox must be a boolean or string');
      }

      // Write the updated settings
      await fs.writeJson(this.settingsFile, mergedSettings, { spaces: 2 });
      
      return mergedSettings;
    } catch (error) {
      throw new Error(`Failed to update settings: ${error.message}`);
    }
  }

  async getGeminiMd() {
    try {
      if (await fs.pathExists(this.geminiMdFile)) {
        return await fs.readFile(this.geminiMdFile, 'utf8');
      }
      return '';
    } catch (error) {
      console.error('Error reading GEMINI.md file:', error);
      return '';
    }
  }

  async updateGeminiMd(content) {
    try {
      await fs.writeFile(this.geminiMdFile, content, 'utf8');
    } catch (error) {
      throw new Error(`Failed to update GEMINI.md: ${error.message}`);
    }
  }

  async validateSettingsJson(jsonString) {
    try {
      const parsed = JSON.parse(jsonString);
      
      // Basic validation of known fields
      const validations = [
        { field: 'theme', type: 'string', optional: true },
        { field: 'autoAccept', type: 'boolean', optional: true },
        { field: 'sandbox', type: ['boolean', 'string'], optional: true },
        { field: 'usageStatisticsEnabled', type: 'boolean', optional: true },
        { field: 'hideTips', type: 'boolean', optional: true }
      ];

      for (const validation of validations) {
        if (parsed[validation.field] !== undefined) {
          const fieldType = typeof parsed[validation.field];
          const expectedTypes = Array.isArray(validation.type) ? validation.type : [validation.type];
          
          if (!expectedTypes.includes(fieldType)) {
            throw new Error(`Field '${validation.field}' must be of type ${expectedTypes.join(' or ')}, got ${fieldType}`);
          }
        }
      }

      return { valid: true, parsed };
    } catch (error) {
      return { valid: false, error: error.message };
    }
  }

  async backupSettings() {
    try {
      const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
      const backupFile = path.join(
        path.dirname(this.settingsFile), 
        `settings.backup.${timestamp}.json`
      );
      
      if (await fs.pathExists(this.settingsFile)) {
        await fs.copy(this.settingsFile, backupFile);
        return backupFile;
      }
      
      return null;
    } catch (error) {
      console.error('Error creating settings backup:', error);
      return null;
    }
  }

  async restoreSettings(backupFile) {
    try {
      if (await fs.pathExists(backupFile)) {
        await fs.copy(backupFile, this.settingsFile);
        return true;
      }
      return false;
    } catch (error) {
      throw new Error(`Failed to restore settings: ${error.message}`);
    }
  }

  async getSettingsInfo() {
    try {
      const stats = await fs.stat(this.settingsFile);
      const settings = await this.getSettings();
      
      return {
        path: this.settingsFile,
        exists: true,
        size: stats.size,
        modified: stats.mtime,
        settingsCount: Object.keys(settings).length
      };
    } catch (error) {
      return {
        path: this.settingsFile,
        exists: false,
        error: error.message
      };
    }
  }
}

module.exports = SettingsManager;
