{"name": "gemini-cli-gui-server", "version": "1.0.0", "description": "Backend server for Gemini CLI GUI", "main": "index.js", "scripts": {"dev": "nodemon index.js", "start": "node index.js"}, "dependencies": {"basic-auth": "^2.0.1", "chokidar": "^3.5.3", "cors": "^2.8.5", "express": "^4.18.2", "fs-extra": "^11.2.0", "node-pty": "^1.0.0", "socket.io": "^4.7.5", "uuid": "^9.0.1"}, "devDependencies": {"nodemon": "^3.0.2"}}