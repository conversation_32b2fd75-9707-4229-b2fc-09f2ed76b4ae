import React, { useState, useEffect } from 'react';
import { X, Save, RotateCcw, FileText, Settings as SettingsIcon, Cloud, Play, Square, Trash2 } from 'lucide-react';
import Editor from '@monaco-editor/react';

const SettingsModal = ({ isOpen, onClose }) => {
  const [activeTab, setActiveTab] = useState('settings');
  const [settingsContent, setSettingsContent] = useState('');
  const [geminiMdContent, setGeminiMdContent] = useState('');
  const [originalSettings, setOriginalSettings] = useState('');
  const [originalGeminiMd, setOriginalGeminiMd] = useState('');
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [error, setError] = useState('');
  const [validationError, setValidationError] = useState('');

  // Tunnel state
  const [tunnelConfig, setTunnelConfig] = useState({
    type: 'quick', // 'quick' or 'named'
    apiToken: '',
    authEnabled: false,
    username: '',
    password: ''
  });
  const [tunnelStatus, setTunnelStatus] = useState(null);
  const [tunnelLoading, setTunnelLoading] = useState(false);

  useEffect(() => {
    if (isOpen) {
      loadData();
      loadTunnelStatus();
    }
  }, [isOpen]);

  const loadData = async () => {
    setLoading(true);
    setError('');

    try {
      // Load settings
      const settingsResponse = await fetch('/api/settings');
      if (!settingsResponse.ok) {
        throw new Error('Failed to load settings');
      }
      const settings = await settingsResponse.json();
      const settingsJson = JSON.stringify(settings, null, 2);
      setSettingsContent(settingsJson);
      setOriginalSettings(settingsJson);

      // Load GEMINI.md
      const geminiMdResponse = await fetch('/api/gemini-md');
      if (!geminiMdResponse.ok) {
        throw new Error('Failed to load GEMINI.md');
      }
      const geminiMdData = await geminiMdResponse.json();
      setGeminiMdContent(geminiMdData.content || '');
      setOriginalGeminiMd(geminiMdData.content || '');
    } catch (err) {
      setError(err.message);
    } finally {
      setLoading(false);
    }
  };

  const validateSettings = async (jsonString) => {
    try {
      const response = await fetch('/api/settings/validate', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ jsonString }),
      });

      const result = await response.json();
      return result;
    } catch (err) {
      return { valid: false, error: 'Failed to validate settings' };
    }
  };

  const handleSaveSettings = async () => {
    setSaving(true);
    setError('');
    setValidationError('');

    try {
      // Validate JSON first
      const validation = await validateSettings(settingsContent);
      if (!validation.valid) {
        setValidationError(validation.error);
        setSaving(false);
        return;
      }

      // Save settings
      const response = await fetch('/api/settings', {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(validation.parsed),
      });

      if (!response.ok) {
        throw new Error('Failed to save settings');
      }

      setOriginalSettings(settingsContent);
      alert('Settings saved successfully!');
    } catch (err) {
      setError(err.message);
    } finally {
      setSaving(false);
    }
  };

  const handleSaveGeminiMd = async () => {
    setSaving(true);
    setError('');

    try {
      const response = await fetch('/api/gemini-md', {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ content: geminiMdContent }),
      });

      if (!response.ok) {
        throw new Error('Failed to save GEMINI.md');
      }

      setOriginalGeminiMd(geminiMdContent);
      alert('GEMINI.md saved successfully!');
    } catch (err) {
      setError(err.message);
    } finally {
      setSaving(false);
    }
  };

  const handleResetSettings = () => {
    if (window.confirm('Are you sure you want to reset your changes?')) {
      setSettingsContent(originalSettings);
      setValidationError('');
    }
  };

  const handleResetGeminiMd = () => {
    if (window.confirm('Are you sure you want to reset your changes?')) {
      setGeminiMdContent(originalGeminiMd);
    }
  };

  // Tunnel management functions
  const loadTunnelStatus = async () => {
    try {
      const response = await fetch('/api/tunnel/status');
      const data = await response.json();
      setTunnelStatus(data);
    } catch (error) {
      console.error('Failed to load tunnel status:', error);
    }
  };

  const handleCreateTunnel = async () => {
    if (tunnelConfig.type === 'named' && !tunnelConfig.apiToken) {
      setError('API token is required for named tunnels');
      return;
    }

    if (tunnelConfig.authEnabled && (!tunnelConfig.username || !tunnelConfig.password)) {
      setError('Username and password are required when authentication is enabled');
      return;
    }

    setTunnelLoading(true);
    setError('');

    try {
      const endpoint = tunnelConfig.type === 'quick' ? '/api/tunnel/create-quick' : '/api/tunnel/create';
      const body = {
        ...tunnelConfig,
        authEnabled: tunnelConfig.authEnabled
      };

      const response = await fetch(endpoint, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(body),
      });

      const data = await response.json();

      if (response.ok) {
        await loadTunnelStatus();
        alert(`${tunnelConfig.type === 'quick' ? 'Quick tunnel' : 'Named tunnel'} created successfully!`);
      } else {
        setError(data.error || 'Failed to create tunnel');
      }
    } catch (error) {
      setError('Failed to create tunnel: ' + error.message);
    } finally {
      setTunnelLoading(false);
    }
  };

  const handleStartTunnel = async () => {
    setTunnelLoading(true);
    setError('');

    try {
      const response = await fetch('/api/tunnel/start', {
        method: 'POST',
      });

      const data = await response.json();

      if (response.ok) {
        await loadTunnelStatus();
      } else {
        setError(data.error || 'Failed to start tunnel');
      }
    } catch (error) {
      setError('Failed to start tunnel: ' + error.message);
    } finally {
      setTunnelLoading(false);
    }
  };

  const handleStopTunnel = async () => {
    setTunnelLoading(true);
    setError('');

    try {
      const response = await fetch('/api/tunnel/stop', {
        method: 'POST',
      });

      const data = await response.json();

      if (response.ok) {
        await loadTunnelStatus();
      } else {
        setError(data.error || 'Failed to stop tunnel');
      }
    } catch (error) {
      setError('Failed to stop tunnel: ' + error.message);
    } finally {
      setTunnelLoading(false);
    }
  };

  const handleDeleteTunnel = async () => {
    if (!window.confirm('Are you sure you want to delete the tunnel? This action cannot be undone.')) {
      return;
    }

    setTunnelLoading(true);
    setError('');

    try {
      const response = await fetch('/api/tunnel', {
        method: 'DELETE',
      });

      const data = await response.json();

      if (response.ok) {
        await loadTunnelStatus();
        setTunnelConfig({ apiToken: '', username: '', password: '' });
      } else {
        setError(data.error || 'Failed to delete tunnel');
      }
    } catch (error) {
      setError('Failed to delete tunnel: ' + error.message);
    } finally {
      setTunnelLoading(false);
    }
  };

  const hasSettingsChanges = settingsContent !== originalSettings;
  const hasGeminiMdChanges = geminiMdContent !== originalGeminiMd;

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-gray-800 rounded-lg w-full max-w-4xl h-5/6 mx-4 flex flex-col">
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b border-gray-700">
          <h2 className="text-xl font-semibold">Settings</h2>
          <button
            onClick={onClose}
            className="p-1 hover:bg-gray-700 rounded transition-colors"
          >
            <X size={20} />
          </button>
        </div>

        {/* Tabs */}
        <div className="flex border-b border-gray-700">
          <button
            onClick={() => setActiveTab('settings')}
            className={`px-6 py-3 font-medium transition-colors flex items-center gap-2 ${
              activeTab === 'settings'
                ? 'text-blue-400 border-b-2 border-blue-400'
                : 'text-gray-400 hover:text-white'
            }`}
          >
            <SettingsIcon size={16} />
            settings.json
            {hasSettingsChanges && <span className="w-2 h-2 bg-yellow-400 rounded-full"></span>}
          </button>
          <button
            onClick={() => setActiveTab('gemini-md')}
            className={`px-6 py-3 font-medium transition-colors flex items-center gap-2 ${
              activeTab === 'gemini-md'
                ? 'text-blue-400 border-b-2 border-blue-400'
                : 'text-gray-400 hover:text-white'
            }`}
          >
            <FileText size={16} />
            GEMINI.md
            {hasGeminiMdChanges && <span className="w-2 h-2 bg-yellow-400 rounded-full"></span>}
          </button>
          <button
            onClick={() => setActiveTab('tunnel')}
            className={`px-6 py-3 font-medium transition-colors flex items-center gap-2 ${
              activeTab === 'tunnel'
                ? 'text-blue-400 border-b-2 border-blue-400'
                : 'text-gray-400 hover:text-white'
            }`}
          >
            <Cloud size={16} />
            Cloudflare Tunnel
            {tunnelStatus?.isRunning && <span className="w-2 h-2 bg-green-400 rounded-full"></span>}
          </button>
        </div>

        {/* Content */}
        <div className="flex-1 flex flex-col">
          {loading ? (
            <div className="flex-1 flex items-center justify-center">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-400"></div>
            </div>
          ) : (
            <>
              {/* Editor */}
              <div className="flex-1">
                {activeTab === 'settings' ? (
                  <Editor
                    height="100%"
                    defaultLanguage="json"
                    value={settingsContent}
                    onChange={(value) => {
                      setSettingsContent(value || '');
                      setValidationError('');
                    }}
                    theme="vs-dark"
                    options={{
                      minimap: { enabled: false },
                      fontSize: 14,
                      lineNumbers: 'on',
                      scrollBeyondLastLine: false,
                      automaticLayout: true,
                    }}
                  />
                ) : activeTab === 'gemini-md' ? (
                  <Editor
                    height="100%"
                    defaultLanguage="markdown"
                    value={geminiMdContent}
                    onChange={(value) => setGeminiMdContent(value || '')}
                    theme="vs-dark"
                    options={{
                      minimap: { enabled: false },
                      fontSize: 14,
                      lineNumbers: 'on',
                      scrollBeyondLastLine: false,
                      automaticLayout: true,
                      wordWrap: 'on',
                    }}
                  />
                ) : (
                  /* Tunnel Configuration */
                  <div className="p-6 space-y-6 overflow-y-auto">
                    <div>
                      <h3 className="text-lg font-semibold mb-4">Cloudflare Tunnel Configuration</h3>
                      <p className="text-gray-400 mb-6">
                        Create a secure tunnel to expose your local Gemini CLI to the internet with password protection.
                      </p>
                    </div>

                    {/* Tunnel Status */}
                    {tunnelStatus?.configured && (
                      <div className="bg-gray-700 rounded-lg p-4 mb-6">
                        <div className="flex items-center justify-between mb-3">
                          <h4 className="font-medium">
                            {tunnelStatus.type === 'quick' ? 'Quick Tunnel' : 'Named Tunnel'} Status
                          </h4>
                          <span className={`px-2 py-1 rounded text-sm ${
                            tunnelStatus.isRunning
                              ? 'bg-green-600 text-green-100'
                              : 'bg-gray-600 text-gray-100'
                          }`}>
                            {tunnelStatus.isRunning ? 'Running' : 'Stopped'}
                          </span>
                        </div>
                        <div className="space-y-2 text-sm">
                          <div><span className="text-gray-400">Type:</span> {tunnelStatus.type === 'quick' ? 'Quick Tunnel' : 'Named Tunnel'}</div>
                          <div><span className="text-gray-400">Name:</span> {tunnelStatus.tunnelName}</div>
                          <div><span className="text-gray-400">ID:</span> {tunnelStatus.tunnelId}</div>
                          {tunnelStatus.tunnelUrl && (
                            <div>
                              <span className="text-gray-400">URL:</span>
                              <a
                                href={tunnelStatus.tunnelUrl}
                                target="_blank"
                                rel="noopener noreferrer"
                                className="text-blue-400 hover:text-blue-300 ml-2"
                              >
                                {tunnelStatus.tunnelUrl}
                              </a>
                            </div>
                          )}
                          {tunnelStatus.auth?.enabled && (
                            <div className="mt-3 p-2 bg-gray-600 rounded">
                              <div className="text-xs text-gray-300 mb-1">Authentication:
                                <span className="ml-2 px-2 py-1 bg-green-600 text-green-100 rounded text-xs">Enabled</span>
                              </div>
                              <div><span className="text-gray-400">Username:</span> {tunnelStatus.auth.username}</div>
                              <div><span className="text-gray-400">Password:</span> {tunnelStatus.auth.password ? '••••••••' : 'Not set'}</div>
                            </div>
                          )}
                          {tunnelStatus.auth && !tunnelStatus.auth.enabled && (
                            <div className="mt-3 p-2 bg-gray-600 rounded">
                              <div className="text-xs text-gray-300">Authentication:
                                <span className="ml-2 px-2 py-1 bg-gray-500 text-gray-200 rounded text-xs">Disabled</span>
                              </div>
                            </div>
                          )}
                        </div>
                        <div className="flex gap-2 mt-4">
                          {tunnelStatus.isRunning ? (
                            <button
                              onClick={handleStopTunnel}
                              disabled={tunnelLoading}
                              className="flex items-center gap-2 px-3 py-2 bg-red-600 hover:bg-red-700 disabled:opacity-50 rounded transition-colors"
                            >
                              <Square size={16} />
                              {tunnelLoading ? 'Stopping...' : 'Stop Tunnel'}
                            </button>
                          ) : (
                            <button
                              onClick={handleStartTunnel}
                              disabled={tunnelLoading}
                              className="flex items-center gap-2 px-3 py-2 bg-green-600 hover:bg-green-700 disabled:opacity-50 rounded transition-colors"
                            >
                              <Play size={16} />
                              {tunnelLoading ? 'Starting...' : 'Start Tunnel'}
                            </button>
                          )}
                          <button
                            onClick={handleDeleteTunnel}
                            disabled={tunnelLoading}
                            className="flex items-center gap-2 px-3 py-2 bg-red-600 hover:bg-red-700 disabled:opacity-50 rounded transition-colors"
                          >
                            <Trash2 size={16} />
                            Delete Tunnel
                          </button>
                        </div>
                      </div>
                    )}

                    {/* Configuration Form */}
                    {!tunnelStatus?.configured && (
                      <div className="space-y-4">
                        {/* Tunnel Type Selection */}
                        <div>
                          <label className="block text-sm font-medium mb-2">
                            Tunnel Type
                          </label>
                          <div className="grid grid-cols-2 gap-2">
                            <button
                              onClick={() => setTunnelConfig(prev => ({ ...prev, type: 'quick' }))}
                              className={`px-4 py-2 rounded-lg border transition-colors ${
                                tunnelConfig.type === 'quick'
                                  ? 'bg-blue-600 border-blue-500 text-white'
                                  : 'bg-gray-700 border-gray-600 text-gray-300 hover:bg-gray-600'
                              }`}
                            >
                              Quick Tunnel
                            </button>
                            <button
                              onClick={() => setTunnelConfig(prev => ({ ...prev, type: 'named' }))}
                              className={`px-4 py-2 rounded-lg border transition-colors ${
                                tunnelConfig.type === 'named'
                                  ? 'bg-blue-600 border-blue-500 text-white'
                                  : 'bg-gray-700 border-gray-600 text-gray-300 hover:bg-gray-600'
                              }`}
                            >
                              Named Tunnel
                            </button>
                          </div>
                          <p className="text-xs text-gray-400 mt-1">
                            {tunnelConfig.type === 'quick'
                              ? 'Quick tunnels are easy to set up but have temporary URLs (trycloudflare.com)'
                              : 'Named tunnels require an API token but provide permanent custom domains'
                            }
                          </p>
                        </div>

                        {/* API Token (only for named tunnels) */}
                        {tunnelConfig.type === 'named' && (
                          <div>
                            <label className="block text-sm font-medium mb-2">
                              Cloudflare API Token
                            </label>
                            <input
                              type="password"
                              value={tunnelConfig.apiToken}
                              onChange={(e) => setTunnelConfig(prev => ({ ...prev, apiToken: e.target.value }))}
                              placeholder="Enter your Cloudflare API token"
                              className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-lg focus:outline-none focus:border-blue-500"
                            />
                            <p className="text-xs text-gray-400 mt-1">
                              Create an API token at cloudflare.com with Zone:Zone:Read and Zone:DNS:Edit permissions
                            </p>
                          </div>
                        )}

                        {/* Authentication Toggle */}
                        <div>
                          <label className="flex items-center gap-3 cursor-pointer">
                            <input
                              type="checkbox"
                              checked={tunnelConfig.authEnabled}
                              onChange={(e) => setTunnelConfig(prev => ({ ...prev, authEnabled: e.target.checked }))}
                              className="w-4 h-4 text-blue-600 bg-gray-700 border-gray-600 rounded focus:ring-blue-500"
                            />
                            <span className="text-sm font-medium">
                              Enable Basic Authentication
                            </span>
                          </label>
                          <p className="text-xs text-gray-400 mt-1 ml-7">
                            When enabled, users will be prompted for username/password when accessing the tunnel
                          </p>
                        </div>

                        {/* Authentication Credentials (only when enabled) */}
                        {tunnelConfig.authEnabled && (
                          <div className="grid grid-cols-2 gap-4">
                            <div>
                              <label className="block text-sm font-medium mb-2">
                                Username
                              </label>
                              <input
                                type="text"
                                value={tunnelConfig.username}
                                onChange={(e) => setTunnelConfig(prev => ({ ...prev, username: e.target.value }))}
                                placeholder="Enter username for tunnel access"
                                className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-lg focus:outline-none focus:border-blue-500"
                              />
                            </div>
                            <div>
                              <label className="block text-sm font-medium mb-2">
                                Password
                              </label>
                              <input
                                type="password"
                                value={tunnelConfig.password}
                                onChange={(e) => setTunnelConfig(prev => ({ ...prev, password: e.target.value }))}
                                placeholder="Enter password for tunnel access"
                                className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-lg focus:outline-none focus:border-blue-500"
                              />
                            </div>
                          </div>
                        )}

                        <button
                          onClick={handleCreateTunnel}
                          disabled={
                            tunnelLoading ||
                            (tunnelConfig.type === 'named' && !tunnelConfig.apiToken) ||
                            (tunnelConfig.authEnabled && (!tunnelConfig.username || !tunnelConfig.password))
                          }
                          className="w-full flex items-center justify-center gap-2 px-4 py-3 bg-blue-600 hover:bg-blue-700 disabled:opacity-50 rounded-lg transition-colors"
                        >
                          <Cloud size={16} />
                          {tunnelLoading ? 'Creating Tunnel...' : `Create ${tunnelConfig.type === 'quick' ? 'Quick' : 'Named'} Tunnel`}
                        </button>
                      </div>
                    )}

                    {/* Instructions */}
                    <div className="bg-gray-700 rounded-lg p-4">
                      <h4 className="font-medium mb-2">Instructions</h4>
                      <div className="space-y-3">
                        <div>
                          <h5 className="text-sm font-medium text-blue-400 mb-1">Quick Tunnel (Recommended for testing)</h5>
                          <ol className="text-sm text-gray-300 space-y-1 list-decimal list-inside ml-2">
                            <li>Select "Quick Tunnel" type</li>
                            <li>Optionally set username/password for basic auth</li>
                            <li>Click "Create Quick Tunnel" - no API token needed!</li>
                            <li>Get an instant trycloudflare.com URL</li>
                          </ol>
                        </div>
                        <div>
                          <h5 className="text-sm font-medium text-green-400 mb-1">Named Tunnel (For production use)</h5>
                          <ol className="text-sm text-gray-300 space-y-1 list-decimal list-inside ml-2">
                            <li>Get a Cloudflare API token from your dashboard</li>
                            <li>Select "Named Tunnel" and enter your API token</li>
                            <li>Set username and password for authentication</li>
                            <li>Create tunnel and configure custom domain</li>
                          </ol>
                        </div>
                      </div>
                    </div>
                  </div>
                )}
              </div>

              {/* Error Messages */}
              {(error || validationError) && (
                <div className="p-4 border-t border-gray-700">
                  <div className="p-3 bg-red-900 border border-red-700 rounded-lg text-red-200 text-sm">
                    {error || validationError}
                  </div>
                </div>
              )}

              {/* Footer */}
              <div className="p-4 border-t border-gray-700 flex items-center justify-between">
                <div className="text-sm text-gray-400">
                  {activeTab === 'settings' ? (
                    <>
                      Edit your Gemini CLI settings. Changes will be saved to ~/.gemini/settings.json
                    </>
                  ) : activeTab === 'gemini-md' ? (
                    <>
                      Edit your global Gemini CLI instructions. Changes will be saved to ~/.gemini/GEMINI.md
                    </>
                  ) : (
                    <>
                      Configure Cloudflare tunnels to securely expose your Gemini CLI to the internet
                    </>
                  )}
                </div>

                <div className="flex items-center gap-3">
                  {activeTab === 'settings' ? (
                    <>
                      <button
                        onClick={handleResetSettings}
                        disabled={!hasSettingsChanges || saving}
                        className="flex items-center gap-2 px-4 py-2 bg-gray-600 hover:bg-gray-500 disabled:opacity-50 rounded-lg transition-colors"
                      >
                        <RotateCcw size={16} />
                        Reset
                      </button>
                      <button
                        onClick={handleSaveSettings}
                        disabled={!hasSettingsChanges || saving}
                        className="flex items-center gap-2 px-4 py-2 bg-blue-600 hover:bg-blue-700 disabled:opacity-50 rounded-lg transition-colors"
                      >
                        <Save size={16} />
                        {saving ? 'Saving...' : 'Save Settings'}
                      </button>
                    </>
                  ) : activeTab === 'gemini-md' ? (
                    <>
                      <button
                        onClick={handleResetGeminiMd}
                        disabled={!hasGeminiMdChanges || saving}
                        className="flex items-center gap-2 px-4 py-2 bg-gray-600 hover:bg-gray-500 disabled:opacity-50 rounded-lg transition-colors"
                      >
                        <RotateCcw size={16} />
                        Reset
                      </button>
                      <button
                        onClick={handleSaveGeminiMd}
                        disabled={!hasGeminiMdChanges || saving}
                        className="flex items-center gap-2 px-4 py-2 bg-blue-600 hover:bg-blue-700 disabled:opacity-50 rounded-lg transition-colors"
                      >
                        <Save size={16} />
                        {saving ? 'Saving...' : 'Save GEMINI.md'}
                      </button>
                    </>
                  ) : null}
                </div>
              </div>
            </>
          )}
        </div>
      </div>
    </div>
  );
};

export default SettingsModal;
