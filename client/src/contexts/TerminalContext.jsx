import React, { createContext, useContext, useState, useEffect, useRef } from 'react';

const TerminalContext = createContext();

export const useTerminal = () => {
  const context = useContext(TerminalContext);
  if (!context) {
    throw new Error('useTerminal must be used within a TerminalProvider');
  }
  return context;
};

export const TerminalProvider = ({ children, socket }) => {
  const [terminals, setTerminals] = useState(new Map());
  const [activeTerminalId, setActiveTerminalId] = useState(null);
  const [sessionStates, setSessionStates] = useState(new Map()); // Track session states separately
  const terminalOutputRef = useRef(new Map());

  useEffect(() => {
    if (!socket) return;

    const handleTerminalOutput = (data) => {
      const { terminalId, sessionId, data: output } = data;
      
      // Store output in ref for immediate access
      if (!terminalOutputRef.current.has(terminalId)) {
        terminalOutputRef.current.set(terminalId, '');
      }
      
      const currentOutput = terminalOutputRef.current.get(terminalId);
      const newOutput = currentOutput + output;
      terminalOutputRef.current.set(terminalId, newOutput);

      // Update state
      setTerminals(prev => {
        const newTerminals = new Map(prev);
        const terminal = newTerminals.get(terminalId) || {
          id: terminalId,
          sessionId,
          output: '',
          status: 'running'
        };
        
        terminal.output = newOutput;
        newTerminals.set(terminalId, terminal);
        return newTerminals;
      });
    };

    const handleTerminalExit = (data) => {
      const { terminalId, code } = data;
      console.log(`Terminal ${terminalId} exited with code ${code}`);

      setTerminals(prev => {
        const newTerminals = new Map(prev);
        const terminal = newTerminals.get(terminalId);
        if (terminal) {
          terminal.status = 'exited';
          terminal.exitCode = code;
          newTerminals.set(terminalId, terminal);
        }
        return newTerminals;
      });
    };

    const handleTerminalError = (data) => {
      const { terminalId, error } = data;
      console.error(`Terminal ${terminalId} error:`, error);

      setTerminals(prev => {
        const newTerminals = new Map(prev);
        const terminal = newTerminals.get(terminalId);
        if (terminal) {
          terminal.status = 'error';
          terminal.error = error;
          newTerminals.set(terminalId, terminal);
        }
        return newTerminals;
      });
    };

    const handleTerminalStatus = (data) => {
      const { terminalId, status } = data;
      console.log(`Terminal ${terminalId} status changed to:`, status);

      setTerminals(prev => {
        const newTerminals = new Map(prev);
        const terminal = newTerminals.get(terminalId);
        if (terminal) {
          terminal.status = status;
          newTerminals.set(terminalId, terminal);
        }
        return newTerminals;
      });
    };

    socket.on('terminal-output', handleTerminalOutput);
    socket.on('terminal-exit', handleTerminalExit);
    socket.on('terminal-error', handleTerminalError);
    socket.on('terminal-status', handleTerminalStatus);

    return () => {
      socket.off('terminal-output', handleTerminalOutput);
      socket.off('terminal-exit', handleTerminalExit);
      socket.off('terminal-error', handleTerminalError);
      socket.off('terminal-status', handleTerminalStatus);
    };
  }, [socket]);

  // Periodically check session states to keep track of background sessions
  useEffect(() => {
    const checkSessionStates = async () => {
      try {
        await getAllSessionStates();
      } catch (error) {
        console.error('Failed to check session states:', error);
      }
    };

    // Check immediately
    checkSessionStates();

    // Then check every 30 seconds
    const interval = setInterval(checkSessionStates, 30000);

    return () => clearInterval(interval);
  }, []);

  const startTerminal = async (projectId, sessionId, projectPath, sessionName, prompt = '') => {
    try {
      const response = await fetch('/api/terminal/start', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          projectId,
          sessionId,
          projectPath,
          sessionName,
          prompt,
        }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to start terminal');
      }

      const { terminalId } = await response.json();
      
      // Initialize terminal in state
      setTerminals(prev => {
        const newTerminals = new Map(prev);
        newTerminals.set(terminalId, {
          id: terminalId,
          sessionId,
          projectId,
          projectPath,
          sessionName,
          output: '',
          status: 'starting'
        });
        return newTerminals;
      });

      setActiveTerminalId(terminalId);
      return terminalId;
    } catch (err) {
      console.error('Error starting terminal:', err);
      throw err;
    }
  };

  const sendInput = async (terminalId, input) => {
    try {
      const response = await fetch(`/api/terminal/${terminalId}/input`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ input }),
      });

      if (!response.ok) {
        throw new Error('Failed to send input');
      }
    } catch (err) {
      console.error('Error sending input:', err);
      throw err;
    }
  };

  const killTerminal = async (terminalId) => {
    try {
      const response = await fetch(`/api/terminal/${terminalId}`, {
        method: 'DELETE',
      });

      if (!response.ok) {
        throw new Error('Failed to kill terminal');
      }

      setTerminals(prev => {
        const newTerminals = new Map(prev);
        newTerminals.delete(terminalId);
        return newTerminals;
      });

      terminalOutputRef.current.delete(terminalId);

      if (activeTerminalId === terminalId) {
        setActiveTerminalId(null);
      }
    } catch (err) {
      console.error('Error killing terminal:', err);
      throw err;
    }
  };

  const resizeTerminal = async (terminalId, cols, rows) => {
    try {
      const response = await fetch(`/api/terminal/${terminalId}/resize`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ cols, rows }),
      });

      if (!response.ok) {
        throw new Error('Failed to resize terminal');
      }
    } catch (err) {
      console.error('Error resizing terminal:', err);
    }
  };

  const getTerminal = (terminalId) => {
    return terminals.get(terminalId);
  };

  const getTerminalBySessionId = (sessionId) => {
    for (const terminal of terminals.values()) {
      if (terminal.sessionId === sessionId) {
        return terminal;
      }
    }
    return null;
  };

  const clearTerminalOutput = (terminalId) => {
    terminalOutputRef.current.set(terminalId, '');
    setTerminals(prev => {
      const newTerminals = new Map(prev);
      const terminal = newTerminals.get(terminalId);
      if (terminal) {
        terminal.output = '';
        newTerminals.set(terminalId, terminal);
      }
      return newTerminals;
    });
  };

  // Session state management functions
  const checkSessionStatus = async (projectId, sessionName) => {
    try {
      const response = await fetch(`/api/session/${projectId}/${sessionName}/status`);
      if (!response.ok) {
        throw new Error('Failed to check session status');
      }
      const status = await response.json();

      // Update session state
      const sessionKey = `${projectId}-${sessionName}`;
      setSessionStates(prev => {
        const newStates = new Map(prev);
        newStates.set(sessionKey, {
          ...status,
          lastChecked: new Date(),
          projectId,
          sessionName
        });
        return newStates;
      });

      return status;
    } catch (error) {
      console.error('Error checking session status:', error);
      return { exists: false, attached: false, name: sessionName };
    }
  };

  const getSessionState = (projectId, sessionName) => {
    const sessionKey = `${projectId}-${sessionName}`;
    return sessionStates.get(sessionKey) || { exists: false, attached: false, name: sessionName };
  };

  const reconnectToSession = async (projectId, sessionId, projectPath, sessionName, prompt = '') => {
    try {
      // Check if session exists first
      const sessionStatus = await checkSessionStatus(projectId, sessionName);

      if (!sessionStatus.exists) {
        throw new Error('Session does not exist, cannot reconnect');
      }

      // Start a new terminal connection to the existing session
      const response = await fetch('/api/terminal/start', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          projectId,
          sessionId,
          projectPath,
          sessionName,
          prompt,
        }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to reconnect to session');
      }

      const { terminalId } = await response.json();

      // Initialize terminal in state
      setTerminals(prev => {
        const newTerminals = new Map(prev);
        newTerminals.set(terminalId, {
          id: terminalId,
          sessionId,
          projectId,
          projectPath,
          sessionName,
          output: '',
          status: 'reconnecting'
        });
        return newTerminals;
      });

      setActiveTerminalId(terminalId);
      return terminalId;
    } catch (err) {
      console.error('Error reconnecting to session:', err);
      throw err;
    }
  };

  const detachFromSession = async (terminalId) => {
    try {
      // Instead of killing the terminal, just mark it as detached
      // The tmux session will continue running in the background
      setTerminals(prev => {
        const newTerminals = new Map(prev);
        const terminal = newTerminals.get(terminalId);
        if (terminal) {
          terminal.status = 'detached';
          newTerminals.set(terminalId, terminal);
        }
        return newTerminals;
      });

      // Don't set activeTerminalId to null here - let the new session take over
      console.log(`[TerminalContext] Detached from terminal ${terminalId}, session continues in background`);
    } catch (err) {
      console.error('Error detaching from terminal:', err);
      throw err;
    }
  };

  const getAllSessionStates = async () => {
    try {
      const response = await fetch('/api/sessions/tmux');
      if (!response.ok) {
        throw new Error('Failed to get session states');
      }
      const tmuxSessions = await response.json();

      // Update session states with current tmux information
      setSessionStates(prev => {
        const newStates = new Map(prev);
        tmuxSessions.forEach(session => {
          // Try to match tmux session names to our project sessions
          const match = session.name.match(/^gemini-(.+)-(.+)$/);
          if (match) {
            const [, projectId, sessionName] = match;
            const sessionKey = `${projectId}-${sessionName}`;
            newStates.set(sessionKey, {
              exists: true,
              attached: session.attached,
              name: session.name,
              created: session.created,
              lastChecked: new Date(),
              projectId,
              sessionName
            });
          }
        });
        return newStates;
      });

      return tmuxSessions;
    } catch (error) {
      console.error('Error getting all session states:', error);
      return [];
    }
  };

  const value = {
    terminals: Array.from(terminals.values()),
    activeTerminalId,
    sessionStates: Array.from(sessionStates.values()),
    startTerminal,
    sendInput,
    killTerminal,
    resizeTerminal,
    getTerminal,
    getTerminalBySessionId,
    clearTerminalOutput,
    setActiveTerminalId,
    checkSessionStatus,
    getSessionState,
    reconnectToSession,
    detachFromSession,
    getAllSessionStates,
  };

  return (
    <TerminalContext.Provider value={value}>
      {children}
    </TerminalContext.Provider>
  );
};
